# Database Schema Documentation

## Overview

The notification engine uses a comprehensive MySQL database schema managed through Prisma ORM. The schema is designed to support multi-channel notifications, template management, trigger automation, and comprehensive audit logging.

## Core Models

### NotificationLog
**Purpose**: Tracks all notification attempts, delivery status, and metadata

```prisma
model NotificationLog {
  id           BigInt             @id @default(autoincrement())
  triggerId    Int?               // Associated trigger
  templateId   Int?               // Template used
  idSite       Int                // Site identifier
  channelId    Int                // Delivery channel
  recipient    String             // Target recipient
  status       NotificationStatus // pending | sent | failed
  triggerValue String             // Trigger condition value
  metadata     Json?              // Additional data
  errorMessage String?            // Failure details
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @default(now())
  createdDate  DateTime?          // Indexed date field
}
```

**Key Features**:
- Comprehensive indexing for performance queries
- Status tracking for delivery confirmation
- JSON metadata for flexible data storage
- Error message logging for debugging

**Indexes**:
- `idx_notification_logs_site_channel_trigger_value`
- `idx_notification_logs_site_created_date`
- `idx_notification_logs_status`

### Trigger
**Purpose**: Defines automation rules and conditions for notifications

```prisma
model Trigger {
  id                   Int                    @id @default(autoincrement())
  name                 String                 @unique
  description          String?
  triggerTypeId        Int                    // Type of trigger
  metricKey            String?               // Metric to evaluate
  isConfigurable       Boolean               @default(false)
  metadata             Json?                 // Additional configuration
  cooldownSeconds      Int?                  // Minimum time between triggers
  publicId             String                @unique @default(dbgenerated("(uuid())"))
  maxTriggerCount      Int?                  // Maximum times to trigger
  maxTriggerPeriod     TriggerIntervalPeriod?
  minIntervalCount     Int?                  // Minimum interval
  minIntervalUnit      TriggerIntervalPeriod?
  fireOnce             Boolean               @default(false)
  deltaThreshold       Decimal?              // Change threshold
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now())
  createdBy            String                @default("system")
  updatedBy            String                @default("system")
  deletedAt            DateTime?             // Soft delete
}
```

**Key Features**:
- Flexible trigger conditions with JSON metadata
- Cooldown periods to prevent spam
- Maximum trigger counts and intervals
- Soft delete capability
- UUID public IDs for external references

### Template
**Purpose**: Stores notification templates with versioning support

```prisma
model Template {
  id            Int              @id @default(autoincrement())
  triggerId     Int?             // Associated trigger
  name          String           
  description   String?
  version       Int              @default(1)
  channelId     Int              // Target channel
  subject       String?          // Email subject or notification title
  body          String           // Template content
  contentTypeId Int              // Content format
  metadata      Json?
  status        TemplateStatus   @default(draft) // draft | archived | published
  publicId      String           @unique @default(dbgenerated("(uuid())"))
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @default(now())
  deletedAt     DateTime?        // Soft delete
  createdBy     String?
  updatedBy     String?
}
```

**Key Features**:
- Template versioning for rollback capability
- Multi-channel support
- Status management (draft, published, archived)
- Audit trail with created/updated by tracking

### Parameter
**Purpose**: Defines parameters for templates and triggers with validation

```prisma
model Parameter {
  id           Int           @id @default(autoincrement())
  triggerId    Int?          // For trigger parameters
  templateId   Int?          // For template parameters
  name         String        
  description  String?
  required     Boolean       @default(false)
  paramTypeId  Int           // Data type
  validations  Json?         // Validation rules
  defaultValue String?       
  exampleValue String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @default(now())
}
```

**Key Features**:
- Flexible parameter definition for templates and triggers
- Type validation with JSON validation rules
- Default and example values for documentation

## Reference Tables

### ChannelType
Defines supported notification channels:
- `email` - SMTP email notifications
- `slack` - Slack workspace notifications  
- `hubspot` - HubSpot CRM notifications

### ContentType
Defines template content formats:
- `html` - HTML formatted email content
- `text` - Plain text content
- `json` - JSON structured data
- `block_kit` - Slack Block Kit format
- `hubspot_crm` - HubSpot CRM format

### ParameterType
Defines parameter data types:
- `string` - Text values
- `number` - Numeric values
- `boolean` - True/false values
- `date` - Date/time values
- `json` - JSON objects
- `array` - Array of values

### TriggerType
Categorizes trigger types:
- `time_based` - Scheduled triggers
- `event_based` - Event-driven triggers
- `condition_based` - Conditional triggers

## Site-Specific Configuration

### SiteNotificationPreference
**Purpose**: User preferences for notification channels per site

```prisma
model SiteNotificationPreference {
  id                Int         @id @default(autoincrement())
  idSite            Int         // Site identifier
  channelId         Int         // Notification channel
  triggerId         Int?        // Specific trigger
  isEnabled         Boolean     @default(false)
  destination       Json?       // Channel-specific config
  triggerNormalized Int?        // Normalized trigger reference
}
```

### SiteTriggerSetting
**Purpose**: Site-specific overrides for trigger configurations

```prisma
model SiteTriggerSetting {
  id                       Int                    @id @default(autoincrement())
  idSite                   Int                    
  triggerId                Int                    
  isEnabled                Boolean                @default(false)
  conditionOverride        Json?                  // Custom conditions
  minIntervalCountOverride Int?                   
  minIntervalUnitOverride  TriggerIntervalPeriod?
  maxTriggerCountOverride  Int?                   
  maxTriggerPeriodOverride TriggerIntervalPeriod?
  deltaThresholdOverride   Decimal?               
}
```

### SiteToken
**Purpose**: Encrypted storage of external service tokens

```prisma
model SiteToken {
  id                   Int         @id @default(autoincrement())
  idSite               Int         
  channelId            Int         
  accessTokenEncrypted String      // AES encrypted token
  iv                   String      // Initialization vector
  tag                  String      // Authentication tag
}
```

**Security Features**:
- AES-256-GCM encryption for tokens
- Unique initialization vectors per token
- Authentication tags for integrity verification

## Audit and Logging

### TemplateAuditLog
**Purpose**: Tracks all template changes for compliance and rollback

```prisma
model TemplateAuditLog {
  id          BigInt               @id @default(autoincrement())
  templateId  Int                  
  action      TemplateAuditAction? // created | edited | published | archived | rolled_back
  fromVersion Int?                 
  toVersion   Int?                 
  performedBy String?              
  reason      String?              
  createdAt   DateTime             @default(now())
}
```

## Enums

### NotificationStatus
```prisma
enum NotificationStatus {
  pending   // Queued for processing
  sent      // Successfully delivered
  failed    // Delivery failed
}
```

### TemplateStatus
```prisma
enum TemplateStatus {
  draft     // Under development
  published // Active and available
  archived  // Deprecated but preserved
}
```

### TriggerIntervalPeriod
```prisma
enum TriggerIntervalPeriod {
  minute
  hour
  day
  week
  month
  year
}
```

### TemplateAuditAction
```prisma
enum TemplateAuditAction {
  created
  edited
  published
  archived
  rolled_back
}
```

## Database Indexes

### Performance Indexes
```sql
-- Notification logs for site queries
CREATE INDEX idx_notification_logs_site_created_date ON notification_logs(id_site, created_date);
CREATE INDEX idx_notification_logs_site_channel_trigger_value ON notification_logs(id_site, channel_id, trigger_value);

-- Template queries
CREATE INDEX idx_templates_channel_id ON templates(channel_id);
CREATE INDEX idx_templates_content_type_id ON templates(content_type_id);

-- Parameter lookups
CREATE INDEX idx_parameters_template_name ON parameters(template_id, name);
CREATE INDEX idx_parameters_trigger_name ON parameters(trigger_id, name);

-- Soft delete queries
CREATE INDEX idx_triggers_deleted_at ON triggers(deleted_at);
CREATE INDEX idx_templates_deleted_at ON templates(deleted_at);
```

## Relationships Diagram

```
NotificationLog
├── belongs_to: Trigger
├── belongs_to: Template
└── belongs_to: ChannelType

Template
├── belongs_to: Trigger
├── belongs_to: ChannelType
├── belongs_to: ContentType
├── has_many: Parameter
├── has_many: NotificationLog
└── has_many: TemplateAuditLog

Trigger
├── belongs_to: TriggerType
├── has_many: Template
├── has_many: Parameter
├── has_many: NotificationLog
├── has_many: SiteTriggerSetting
└── has_many: SiteNotificationPreference

SiteNotificationPreference
├── belongs_to: ChannelType
└── belongs_to: Trigger

SiteTriggerSetting
└── belongs_to: Trigger

SiteToken
└── belongs_to: ChannelType
```

## Migration Strategy

### Adding New Columns
```sql
-- Use nullable columns for backward compatibility
ALTER TABLE triggers ADD COLUMN new_feature_flag BOOLEAN DEFAULT FALSE;

-- Update existing records if needed
UPDATE triggers SET new_feature_flag = TRUE WHERE condition;
```

### Schema Evolution
1. **Add new columns as nullable**
2. **Update application code to handle both states**
3. **Migrate data in batches**
4. **Make columns non-nullable if required**
5. **Clean up old columns after full deployment**

## Backup and Recovery

### Backup Strategy
```bash
# Full database backup
mysqldump -u user -p notifications > backup_$(date +%Y%m%d_%H%M%S).sql

# Table-specific backup
mysqldump -u user -p notifications notification_logs > notification_logs_backup.sql
```

### Point-in-Time Recovery
- Binary logging enabled for transaction recovery
- Regular full backups with incremental log backups
- Testing recovery procedures monthly

## Performance Optimization

### Query Optimization
- Use composite indexes for multi-column WHERE clauses
- Avoid SELECT * queries, specify columns
- Use LIMIT for pagination queries
- Consider read replicas for reporting queries

### Table Maintenance
```sql
-- Analyze table statistics
ANALYZE TABLE notification_logs;

-- Optimize table structure
OPTIMIZE TABLE notification_logs;

-- Check table integrity
CHECK TABLE notification_logs;
```

### Monitoring Queries
```sql
-- Find slow queries
SELECT * FROM information_schema.PROCESSLIST WHERE Time > 60;

-- Check index usage
SHOW INDEX FROM notification_logs;

-- Table size analysis
SELECT 
  table_name,
  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'notifications';
```
