# Troubleshooting Guide & FAQ

This document provides solutions to common issues, debugging techniques, and frequently asked questions for the Notification Engine System.

## Table of Contents

- [Quick Diagnostics](#quick-diagnostics)
- [Common Issues](#common-issues)
- [Database Issues](#database-issues)
- [Queue System Issues](#queue-system-issues)
- [Template Issues](#template-issues)
- [API Issues](#api-issues)
- [Performance Issues](#performance-issues)
- [Security Issues](#security-issues)
- [Monitoring & Debugging](#monitoring--debugging)
- [FAQ](#frequently-asked-questions)

## Quick Diagnostics

### Health Check Commands

```bash
# Basic application health
curl http://localhost:3000/up

# Detailed health check
curl http://localhost:3000/health

# Queue status
curl http://localhost:3000/admin/queues

# Database connectivity
curl http://localhost:3000/health/database
```

### Log Analysis

```bash
# Real-time application logs
docker logs notification-engine --follow

# Filter error logs
docker logs notification-engine 2>&1 | grep "ERROR"

# Structured log queries with jq
docker logs notification-engine 2>&1 | jq '.level == "error"'

# Performance monitoring
docker logs notification-engine 2>&1 | jq '.responseTime > 1000'
```

### Resource Monitoring

```bash
# Container resource usage
docker stats notification-engine

# Memory usage breakdown
docker exec notification-engine node -e "console.log(process.memoryUsage())"

# CPU and memory limits
docker inspect notification-engine | jq '.[0].HostConfig'
```

## Common Issues

### 1. Application Won't Start

**Symptoms:**
- Container exits immediately
- "Connection refused" errors
- Environment validation failures

**Diagnostics:**
```bash
# Check container logs
docker logs notification-engine

# Verify environment variables
docker exec notification-engine env | grep -E "(DATABASE_URL|REDIS_HOST|RABBITMQ_HOST)"

# Test database connection
docker exec notification-engine npx prisma db push --dry-run
```

**Solutions:**

```bash
# Fix environment variables
cp env.sample .env
# Edit .env with proper values

# Test database connectivity
mysql -h localhost -u root -p -e "SELECT 1;"

# Regenerate Prisma client
docker exec notification-engine npx prisma generate

# Reset database if needed
docker exec notification-engine npx prisma migrate reset
```

### 2. High Memory Usage

**Symptoms:**
- Container getting killed (OOMKilled)
- Slow response times
- Memory warnings in logs

**Diagnostics:**
```bash
# Check memory usage patterns
docker stats notification-engine --no-stream

# Analyze heap dump (if available)
docker exec notification-engine node --inspect-brk=0.0.0.0:9229 dist/server.js

# Memory leak detection
curl http://localhost:3000/metrics | grep nodejs_heap
```

**Solutions:**

```javascript
// Add to server.ts - Memory monitoring
setInterval(() => {
  const usage = process.memoryUsage();
  if (usage.heapUsed > 500 * 1024 * 1024) { // 500MB threshold
    console.warn('High memory usage:', usage);
  }
}, 30000);

// Force garbage collection periodically
if (global.gc) {
  setInterval(() => {
    global.gc();
  }, 60000);
}
```

```yaml
# Docker memory limits
services:
  app:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

### 3. Slow API Response Times

**Symptoms:**
- API calls taking > 5 seconds
- Timeout errors
- Queue processing delays

**Diagnostics:**
```bash
# API response time testing
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:3000/trigger/evaluation

# Database query analysis
docker exec mysql mysql -u root -p -e "SHOW PROCESSLIST;"
docker exec mysql mysql -u root -p -e "SHOW ENGINE INNODB STATUS\G"

# Queue processing times
curl http://localhost:3000/admin/queues | jq '.queues[].processing_time'
```

**Solutions:**

```sql
-- Add database indexes
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_triggers_active ON triggers(is_active);

-- Optimize queries
ANALYZE TABLE notifications;
OPTIMIZE TABLE notifications;
```

```typescript
// Add query optimization
const notifications = await prisma.notification.findMany({
  where: { status: 'PENDING' },
  select: { id: true, recipient: true }, // Only select needed fields
  take: 100, // Limit results
  orderBy: { createdAt: 'asc' }
});
```

## Database Issues

### Connection Pool Exhaustion

**Symptoms:**
- "Too many connections" errors
- Slow database queries
- Connection timeout errors

**Diagnostics:**
```sql
-- Check current connections
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';

-- Check connection pool settings
SHOW VARIABLES LIKE 'max_connections';
```

**Solutions:**

```typescript
// Optimize Prisma connection pool
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  log: ['query', 'info', 'warn', 'error'],
});

// Connection pool configuration
DATABASE_URL="mysql://user:password@localhost:3306/db?connection_limit=10&pool_timeout=20"
```

```sql
-- Increase MySQL connection limit
SET GLOBAL max_connections = 500;

-- Add to my.cnf
[mysqld]
max_connections = 500
innodb_buffer_pool_size = 1G
```

### Migration Issues

**Symptoms:**
- Schema drift warnings
- Migration conflicts
- Data loss during migrations

**Diagnostics:**
```bash
# Check migration status
npx prisma migrate status

# View pending migrations
npx prisma migrate diff --from-schema-datamodel prisma/schema.prisma

# Database schema inspection
npx prisma db pull
```

**Solutions:**

```bash
# Resolve migration conflicts
npx prisma migrate resolve --applied "migration-name"

# Reset database (development only)
npx prisma migrate reset

# Create and apply migration
npx prisma migrate dev --name "fix-schema"

# Deploy to production
npx prisma migrate deploy
```

### Data Consistency Issues

**Symptoms:**
- Duplicate notifications
- Missing foreign key relationships
- Orphaned records

**Diagnostics:**
```sql
-- Find orphaned notifications
SELECT n.id, n.trigger_id 
FROM notifications n 
LEFT JOIN triggers t ON n.trigger_id = t.id 
WHERE t.id IS NULL;

-- Check for duplicates
SELECT recipient, template_id, COUNT(*) 
FROM notifications 
GROUP BY recipient, template_id 
HAVING COUNT(*) > 1;
```

**Solutions:**

```sql
-- Clean up orphaned records
DELETE FROM notifications 
WHERE trigger_id NOT IN (SELECT id FROM triggers);

-- Add constraints
ALTER TABLE notifications 
ADD CONSTRAINT fk_notification_trigger 
FOREIGN KEY (trigger_id) REFERENCES triggers(id) ON DELETE CASCADE;

-- Fix duplicate prevention
CREATE UNIQUE INDEX idx_unique_notification 
ON notifications(recipient, template_id, site_id, created_at);
```

## Queue System Issues

### Redis Connection Issues

**Symptoms:**
- Queue jobs not processing
- Redis connection errors
- BullMQ dashboard not loading

**Diagnostics:**
```bash
# Test Redis connectivity
redis-cli -h localhost -p 6379 ping

# Check Redis memory usage
redis-cli info memory

# Monitor Redis commands
redis-cli monitor
```

**Solutions:**

```bash
# Redis configuration tuning
# In redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
timeout 300

# Restart Redis
docker restart redis

# Clear Redis cache if needed
redis-cli flushall
```

```typescript
// Redis connection with retry logic
const redis = new Redis({
  host: process.env.REDIS_HOST,
  port: Number(process.env.REDIS_PORT),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  connectTimeout: 10000,
  commandTimeout: 5000
});
```

### RabbitMQ Connection Issues

**Symptoms:**
- Messages not being consumed
- Connection timeout errors
- Queue backing up

**Diagnostics:**
```bash
# Check RabbitMQ status
rabbitmqctl status

# List queues and their status
rabbitmqctl list_queues name messages consumers

# Check connections
rabbitmqctl list_connections
```

**Solutions:**

```bash
# Restart RabbitMQ
docker restart rabbitmq

# Purge stuck queues
rabbitmqctl purge_queue evaluation_queue

# Reset RabbitMQ (development only)
rabbitmqctl reset
```

```typescript
// RabbitMQ connection with retry
const connection = await amqp.connect(RABBITMQ_URL, {
  heartbeat: 60,
  connection_timeout: 10000,
  clientProperties: {
    connection_name: 'notification-engine'
  }
});

connection.on('error', (err) => {
  console.error('RabbitMQ connection error:', err);
  // Implement reconnection logic
});
```

### Queue Job Failures

**Symptoms:**
- Jobs stuck in "failed" state
- High retry counts
- Processing timeouts

**Diagnostics:**
```bash
# Check failed jobs
curl http://localhost:3000/admin/queues | jq '.queues[].failed'

# Get job details
curl http://localhost:3000/admin/queues/evaluation/failed
```

**Solutions:**

```typescript
// Improve job error handling
export class NotificationJob {
  async process(data: any) {
    try {
      await this.sendNotification(data);
    } catch (error) {
      if (error.code === 'RATE_LIMIT') {
        // Retry after delay
        throw new Error('Rate limited, retry later');
      } else if (error.code === 'INVALID_EMAIL') {
        // Don't retry for permanent failures
        throw new Error(`Permanent failure: ${error.message}`);
      }
      throw error;
    }
  }
}

// Configure job options
const jobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 5000,
  },
  removeOnComplete: 10,
  removeOnFail: 5
};
```

## Template Issues

### Template Rendering Errors

**Symptoms:**
- Email/Slack notifications not rendering
- Handlebars compilation errors
- Missing template data

**Diagnostics:**
```bash
# Test template rendering
curl -X POST http://localhost:3000/admin/test-template \
  -H "Content-Type: application/json" \
  -d '{
    "templateId": "weekly-summary",
    "channel": "email",
    "data": {"siteName": "Test"}
  }'
```

**Solutions:**

```typescript
// Add template validation
export const validateTemplateData = (templateId: string, data: any) => {
  const requiredFields = templateRegistry[templateId]?.requiredFields || [];
  const missing = requiredFields.filter(field => !data[field]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`);
  }
};

// Safe template rendering
export const renderTemplate = async (templateId: string, data: any) => {
  try {
    validateTemplateData(templateId, data);
    return await templateEngine.render(templateId, data);
  } catch (error) {
    console.error(`Template rendering failed for ${templateId}:`, error);
    throw new Error(`Template rendering failed: ${error.message}`);
  }
};
```

### Missing Template Files

**Symptoms:**
- "Template not found" errors
- 404 errors for template endpoints
- Blank notification content

**Diagnostics:**
```bash
# Check template files
ls -la src/new-templates/
ls -la src/slack-templates/

# Verify template registry
node -e "console.log(require('./dist/template-registry').templateRegistry)"
```

**Solutions:**

```bash
# Copy missing templates
cp -r templates/* src/new-templates/
cp -r slack-templates/* src/slack-templates/

# Rebuild with templates
npm run build
```

```typescript
// Add template existence check
const checkTemplateExists = (templateId: string, channel: string) => {
  const templatePath = path.join(
    __dirname, 
    `../templates/${channel}`, 
    `${templateId}.hbs`
  );
  
  if (!fs.existsSync(templatePath)) {
    throw new Error(`Template not found: ${templateId} for ${channel}`);
  }
};
```

## API Issues

### Authentication Problems

**Symptoms:**
- 401 Unauthorized errors
- API key validation failures
- Session timeout issues

**Diagnostics:**
```bash
# Test API authentication
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/api/triggers

# Check token validity
curl -X POST http://localhost:3000/auth/validate \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Solutions:**

```typescript
// Improve auth middleware
export const authenticateRequest = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Missing authorization token' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    next();
  } catch (error) {
    console.error('Auth error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};
```

### Rate Limiting Issues

**Symptoms:**
- 429 Too Many Requests errors
- API calls being blocked
- Legitimate traffic getting limited

**Diagnostics:**
```bash
# Check rate limit headers
curl -I http://localhost:3000/api/triggers

# Monitor rate limit metrics
curl http://localhost:3000/metrics | grep rate_limit
```

**Solutions:**

```typescript
// Configurable rate limiting
import rateLimit from 'express-rate-limit';

const createRateLimit = (windowMs: number, max: number) => 
  rateLimit({
    windowMs,
    max,
    message: 'Too many requests, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
  });

// Different limits for different endpoints
app.use('/api/triggers', createRateLimit(15 * 60 * 1000, 100)); // 100 requests per 15 minutes
app.use('/real-time', createRateLimit(60 * 1000, 10)); // 10 requests per minute
```

## Performance Issues

### High CPU Usage

**Symptoms:**
- Container using > 80% CPU
- Slow API responses
- Queue processing delays

**Diagnostics:**
```bash
# Monitor CPU usage
docker stats notification-engine --no-stream

# Profile Node.js application
docker exec notification-engine node --prof dist/server.js

# Analyze CPU usage patterns
top -p $(docker inspect -f '{{.State.Pid}}' notification-engine)
```

**Solutions:**

```typescript
// Add CPU monitoring
const os = require('os');

setInterval(() => {
  const load = os.loadavg();
  if (load[0] > os.cpus().length) {
    console.warn('High CPU load detected:', load);
  }
}, 30000);

// Optimize heavy operations
const processInBatches = async (items: any[], batchSize = 10) => {
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    await Promise.all(batch.map(processItem));
    
    // Allow event loop to process other tasks
    await new Promise(resolve => setImmediate(resolve));
  }
};
```

### Memory Leaks

**Symptoms:**
- Steadily increasing memory usage
- Out of memory errors
- Container restarts

**Diagnostics:**
```bash
# Track memory over time
while true; do
  docker stats notification-engine --no-stream | grep notification-engine
  sleep 60
done

# Generate heap snapshot
kill -USR2 $(docker inspect -f '{{.State.Pid}}' notification-engine)
```

**Solutions:**

```typescript
// Add memory monitoring
const monitorMemory = () => {
  const usage = process.memoryUsage();
  const threshold = 800 * 1024 * 1024; // 800MB
  
  if (usage.heapUsed > threshold) {
    console.warn('Memory usage high:', usage);
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  }
};

setInterval(monitorMemory, 60000);

// Proper event listener cleanup
const cleanup = () => {
  // Remove event listeners
  process.removeAllListeners();
  
  // Close database connections
  prisma.$disconnect();
  
  // Close Redis connections
  redis.disconnect();
};

process.on('SIGTERM', cleanup);
process.on('SIGINT', cleanup);
```

## Security Issues

### API Security

**Symptoms:**
- Unauthorized access attempts
- SQL injection attempts
- Brute force attacks

**Diagnostics:**
```bash
# Check access logs
docker logs notification-engine | grep -E "(401|403|429)"

# Monitor for suspicious activity
docker logs notification-engine | grep -E "DROP TABLE|UNION SELECT|<script"
```

**Solutions:**

```typescript
// Security middleware stack
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import validator from 'validator';

app.use(helmet());
app.use(rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100
}));

// Input validation
const validateInput = (req: Request, res: Response, next: NextFunction) => {
  // Sanitize strings
  for (const [key, value] of Object.entries(req.body)) {
    if (typeof value === 'string') {
      req.body[key] = validator.escape(value);
    }
  }
  next();
};

app.use(validateInput);
```

### Environment Security

**Symptoms:**
- Exposed sensitive information
- Weak authentication
- Unencrypted connections

**Solutions:**

```bash
# Use proper secrets management
export DATABASE_PASSWORD=$(aws secretsmanager get-secret-value --secret-id prod/db/password --query SecretString --output text)

# Enable SSL/TLS
export DATABASE_URL="mysql://user:password@host:3306/db?ssl=true&sslcert=/path/to/cert.pem"

# Rotate secrets regularly
aws secretsmanager rotate-secret --secret-id prod/api/keys
```

## Monitoring & Debugging

### Application Monitoring

```typescript
// Advanced logging setup
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info({
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
  });
  
  next();
});
```

### Performance Monitoring

```typescript
// Performance tracking
const performanceTracker = {
  track: (operation: string, fn: Function) => {
    return async (...args: any[]) => {
      const start = process.hrtime.bigint();
      
      try {
        const result = await fn(...args);
        const end = process.hrtime.bigint();
        const duration = Number(end - start) / 1000000; // Convert to milliseconds
        
        logger.info({
          operation,
          duration,
          status: 'success'
        });
        
        return result;
      } catch (error) {
        const end = process.hrtime.bigint();
        const duration = Number(end - start) / 1000000;
        
        logger.error({
          operation,
          duration,
          status: 'error',
          error: error.message
        });
        
        throw error;
      }
    };
  }
};

// Usage
const sendEmail = performanceTracker.track('sendEmail', async (data) => {
  // Email sending logic
});
```

## Frequently Asked Questions

### Q: Why are notifications not being sent?

**A:** Check the following in order:
1. Verify `EMAIL_ON=true` in environment variables
2. Check SMTP configuration and credentials
3. Verify queue processing is running
4. Check for failed jobs in the queue dashboard
5. Verify template exists and renders correctly

### Q: How do I add a new notification template?

**A:** Follow these steps:
1. Create template files in `src/new-templates/` (email) and `src/slack-templates/` (Slack)
2. Add template definition to `template-registry.ts`
3. Define validation schema in the registry
4. Test template rendering
5. Deploy and verify

### Q: Why is the queue processing slowly?

**A:** Common causes:
1. Redis memory exhaustion - check with `redis-cli info memory`
2. Database connection pool exhaustion
3. Rate limiting from external services (SMTP, Slack)
4. Insufficient worker processes
5. Resource constraints (CPU/memory)

### Q: How do I scale the application?

**A:** Scaling options:
1. **Horizontal scaling**: Deploy multiple instances behind a load balancer
2. **Database scaling**: Use read replicas for read-heavy operations
3. **Queue scaling**: Use Redis Cluster and multiple RabbitMQ nodes
4. **Caching**: Implement Redis caching for frequently accessed data

### Q: How do I backup and restore data?

**A:** Backup procedures:
```bash
# Database backup
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD notifications > backup.sql

# Redis backup
redis-cli --rdb backup.rdb

# Restore database
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD notifications < backup.sql
```

### Q: How do I monitor application health?

**A:** Use these endpoints and tools:
- `/up` - Basic health check
- `/health` - Detailed health with service status
- `/admin/queues` - Queue monitoring
- `/metrics` - Prometheus metrics
- Docker logs and stats for resource monitoring

### Q: What's the disaster recovery procedure?

**A:** Recovery steps:
1. **Assess the situation**: Check logs and monitoring alerts
2. **Scale up resources**: Increase container count if needed
3. **Database recovery**: Restore from backup if corrupted
4. **Queue recovery**: Purge stuck queues and restart processing
5. **Verify functionality**: Run health checks and test notifications
6. **Monitor closely**: Watch for recurring issues

### Q: How do I update to a new version?

**A:** Update procedure:
1. **Test in staging**: Deploy to staging environment first
2. **Backup data**: Create database and Redis backups
3. **Run migrations**: Apply any database schema changes
4. **Deploy gradually**: Use blue-green or rolling deployment
5. **Monitor health**: Watch logs and metrics during deployment
6. **Rollback if needed**: Have rollback plan ready

This troubleshooting guide should help you diagnose and resolve most issues with the Notification Engine System. For complex issues, consider enabling debug logging and consulting the application logs for more detailed information.
