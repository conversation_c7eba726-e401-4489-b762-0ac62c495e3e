# API Documentation

## Overview

The Notification Engine provides a RESTful API for managing notifications, triggers, templates, and integrations. The API is built with Express.js and includes comprehensive middleware for validation, authentication, and error handling.

## Base Configuration

- **Base URL**: `http://localhost:3000` (development) 
- **Content-Type**: `application/json`
- **Authentication**: Bearer token (when implemented)

## Core Endpoints

### Health Check

#### GET /up
Check the health status of the application.

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-06-09T10:30:00.000Z",
  "uptime": 1800,
  "version": "1.0.0"
}
```

**Status Codes:**
- `200` - Service is healthy
- `503` - Service is unhealthy

---

### Trigger Management

#### POST /trigger/evaluation
Trigger evaluation of sites for notification conditions.

**Request Body:**
```json
{
  "sites": [123, 456, 789],
  "triggerTypes": ["trial", "engagement", "revenue"],
  "immediate": true,
  "metadata": {
    "source": "manual",
    "requestedBy": "admin"
  }
}
```

**Parameters:**
- `sites` (required): Array of site IDs to evaluate
- `triggerTypes` (optional): Specific trigger types to check
- `immediate` (optional): Skip scheduling and process immediately
- `metadata` (optional): Additional context information

**Response:**
```json
{
  "success": true,
  "evaluationId": "eval_123456789",
  "sitesQueued": 3,
  "estimatedProcessingTime": "2-5 minutes"
}
```

**Status Codes:**
- `200` - Evaluation queued successfully
- `400` - Invalid request parameters
- `500` - Internal server error

---

### Real-time Notifications

#### POST /real-time
Send immediate notifications without trigger evaluation.

**Request Body:**
```json
{
  "idSite": 123,
  "triggerName": "login-reminder",
  "templateData": {
    "userName": "John Doe",
    "daysSinceLogin": 7,
    "loginUrl": "https://app.example.com/login"
  },
  "channels": ["email", "slack"],
  "priority": "high",
  "scheduledFor": "2025-06-09T15:30:00.000Z"
}
```

**Parameters:**
- `idSite` (required): Site identifier
- `triggerName` (required): Name of trigger/template to use
- `templateData` (required): Data for template rendering
- `channels` (optional): Specific channels to send to (default: all enabled)
- `priority` (optional): Message priority (low, normal, high)
- `scheduledFor` (optional): Schedule for future delivery

**Response:**
```json
{
  "success": true,
  "notificationId": "notif_987654321",
  "status": "queued",
  "channels": {
    "email": {
      "status": "queued",
      "estimatedDelivery": "2025-06-09T10:35:00.000Z"
    },
    "slack": {
      "status": "queued",
      "estimatedDelivery": "2025-06-09T10:31:00.000Z"
    }
  }
}
```

**Status Codes:**
- `200` - Notification queued successfully
- `400` - Invalid request parameters
- `422` - Template validation failed
- `500` - Internal server error

---

### Slack Integration

#### POST /slack/auth
Handle Slack OAuth authentication flow.

**Request Body:**
```json
{
  "code": "oauth_code_from_slack",
  "state": "random_state_string",
  "idSite": 123
}
```

**Response:**
```json
{
  "success": true,
  "teamName": "Example Team",
  "channelId": "C1234567890",
  "channelName": "#notifications",
  "accessToken": "encrypted_token_reference"
}
```

#### POST /slack/message
Send a direct Slack message (for testing/admin use).

**Request Body:**
```json
{
  "idSite": 123,
  "message": "Test notification message",
  "subject": "Test Subject",
  "channel": "#general"
}
```

---

## Admin Endpoints

### Queue Monitoring

#### GET /admin/queues
Access the Bull Board dashboard for queue monitoring.

**Response:** HTML dashboard interface

**Features:**
- Real-time queue statistics
- Job details and logs
- Manual job management
- Performance metrics

---

## Error Handling

### Standard Error Response

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Request validation failed",
    "details": {
      "field": "sites",
      "issue": "Array must contain at least one site ID"
    },
    "timestamp": "2025-06-09T10:30:00.000Z",
    "requestId": "req_123456789"
  }
}
```

### Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Request validation failed | 400 |
| `TEMPLATE_NOT_FOUND` | Template does not exist | 404 |
| `TEMPLATE_VALIDATION_ERROR` | Template data validation failed | 422 |
| `SITE_NOT_FOUND` | Site ID not found | 404 |
| `RATE_LIMIT_EXCEEDED` | Too many requests | 429 |
| `QUEUE_UNAVAILABLE` | Queue service unavailable | 503 |
| `INTERNAL_ERROR` | Unexpected server error | 500 |

---

## Request/Response Examples

### Trigger Weekly Summary

```http
POST /trigger/evaluation
Content-Type: application/json

{
  "sites": [123],
  "triggerTypes": ["weekly-summary"],
  "immediate": true
}
```

```json
{
  "success": true,
  "evaluationId": "eval_202506091030_123",
  "sitesQueued": 1,
  "estimatedProcessingTime": "1-2 minutes"
}
```

### Send Login Reminder

```http
POST /real-time
Content-Type: application/json

{
  "idSite": 123,
  "triggerName": "login-case-study",
  "templateData": {
    "userName": "Sarah Johnson",
    "siteName": "E-commerce Store",
    "daysSinceLogin": 14,
    "caseStudyUrl": "https://example.com/case-study",
    "loginUrl": "https://app.example.com/login"
  },
  "channels": ["email"]
}
```

```json
{
  "success": true,
  "notificationId": "notif_20250609103045_123",
  "status": "queued",
  "channels": {
    "email": {
      "status": "queued",
      "recipient": "<EMAIL>",
      "estimatedDelivery": "2025-06-09T10:32:00.000Z"
    }
  }
}
```

### Send Team Invitation Reminder

```http
POST /real-time
Content-Type: application/json

{
  "idSite": 456,
  "triggerName": "team-not-invited",
  "templateData": {
    "userName": "Mike Chen",
    "siteName": "SaaS Dashboard",
    "inviteTeamUrl": "https://app.example.com/team/invite",
    "benefitsText": "Collaborating with your team can increase productivity by 40%"
  },
  "channels": ["email", "slack"]
}
```

---

## Middleware

### Request Validation

All endpoints include request validation middleware using Zod schemas:

```typescript
// Example validation schema
const triggerEvaluationSchema = z.object({
  sites: z.array(z.number().positive()).min(1),
  triggerTypes: z.array(z.string()).optional(),
  immediate: z.boolean().optional(),
  metadata: z.record(z.any()).optional()
});
```

### Error Handling

Global error handling middleware catches and formats all errors:

```typescript
app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  const errorResponse = {
    error: {
      code: error.name || 'INTERNAL_ERROR',
      message: error.message,
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] || generateRequestId()
    }
  };
  
  if (error instanceof ValidationError) {
    res.status(400).json(errorResponse);
  } else if (error instanceof NotFoundError) {
    res.status(404).json(errorResponse);
  } else {
    res.status(500).json(errorResponse);
  }
});
```

### Database Connection Management

Automatic Prisma connection management:

```typescript
export const disconnectPrismaV1 = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Skip for monitoring endpoints
  if (EXCLUDED_PATHS.some(path => req.originalUrl.startsWith(path))) {
    return next();
  }
  
  let isCleanUp = false;
  const cleanup = async (event: string) => {
    if (isCleanUp) return;
    
    try {
      isCleanUp = true;
      await prisma.$disconnect();
      console.log(`Prisma disconnect activated in ${event}`);
    } catch (err) {
      console.error(`Prisma disconnect failed in ${event}`);
    }
  };

  res.on('close', () => cleanup('close'));
  res.on('finish', () => cleanup('finish'));
  
  next();
};
```

---

## Authentication & Security

### Security Headers

```typescript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

### CORS Configuration

```typescript
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourapp.com', 'https://dashboard.yourapp.com']
    : true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
}));
```

### Rate Limiting (Recommended)

```typescript
import rateLimit from 'express-rate-limit';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP'
    }
  }
});

app.use('/api/', limiter);
```

---

## WebSocket Events (Future Enhancement)

### Real-time Updates

For future implementation of real-time notifications:

```typescript
// WebSocket event types
interface NotificationEvent {
  type: 'notification.sent' | 'notification.failed' | 'notification.delivered';
  data: {
    notificationId: string;
    idSite: number;
    channel: string;
    status: string;
    timestamp: string;
  };
}

// Client subscription
ws.send(JSON.stringify({
  type: 'subscribe',
  channels: ['notifications', 'triggers'],
  siteId: 123
}));
```

---

## API Client Examples

### JavaScript/Node.js

```javascript
class NotificationAPI {
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }
  
  async triggerEvaluation(sites, options = {}) {
    const response = await fetch(`${this.baseUrl}/trigger/evaluation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        sites,
        ...options
      })
    });
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    
    return response.json();
  }
  
  async sendRealTimeNotification(notification) {
    const response = await fetch(`${this.baseUrl}/real-time`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify(notification)
    });
    
    return response.json();
  }
}

// Usage
const api = new NotificationAPI('http://localhost:3000', 'your-api-key');

await api.triggerEvaluation([123, 456], {
  triggerTypes: ['weekly-summary'],
  immediate: true
});

await api.sendRealTimeNotification({
  idSite: 123,
  triggerName: 'login-reminder',
  templateData: {
    userName: 'John Doe',
    daysSinceLogin: 7
  }
});
```

### Python

```python
import requests
import json

class NotificationAPI:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }
    
    def trigger_evaluation(self, sites, **options):
        data = {'sites': sites, **options}
        response = requests.post(
            f'{self.base_url}/trigger/evaluation',
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def send_real_time_notification(self, notification):
        response = requests.post(
            f'{self.base_url}/real-time',
            headers=self.headers,
            json=notification
        )
        response.raise_for_status()
        return response.json()

# Usage
api = NotificationAPI('http://localhost:3000', 'your-api-key')

api.trigger_evaluation([123, 456], trigger_types=['weekly-summary'])

api.send_real_time_notification({
    'idSite': 123,
    'triggerName': 'login-reminder',
    'templateData': {
        'userName': 'John Doe',
        'daysSinceLogin': 7
    }
})
```

---

## Testing

### Unit Tests

```typescript
describe('API Endpoints', () => {
  test('POST /trigger/evaluation', async () => {
    const response = await request(app)
      .post('/trigger/evaluation')
      .send({
        sites: [123],
        immediate: true
      })
      .expect(200);
    
    expect(response.body.success).toBe(true);
    expect(response.body.evaluationId).toBeDefined();
  });
  
  test('POST /real-time validation', async () => {
    const response = await request(app)
      .post('/real-time')
      .send({
        // Missing required fields
      })
      .expect(400);
    
    expect(response.body.error.code).toBe('VALIDATION_ERROR');
  });
});
```

### Integration Tests

```typescript
describe('Notification Flow', () => {
  test('should send email notification', async () => {
    const mockEmailSend = jest.fn();
    jest.spyOn(emailConnector, 'send').mockImplementation(mockEmailSend);
    
    await request(app)
      .post('/real-time')
      .send({
        idSite: 123,
        triggerName: 'weekly-summary',
        templateData: { /* test data */ },
        channels: ['email']
      })
      .expect(200);
    
    // Verify queue processing
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    expect(mockEmailSend).toHaveBeenCalledWith({
      recipient: '<EMAIL>',
      subject: expect.stringContaining('Weekly Summary'),
      content: expect.stringContaining('Test Site')
    });
  });
});
```

---

## Performance Considerations

### Response Time Targets

- **Health checks**: < 100ms
- **Trigger evaluation**: < 500ms (queueing)
- **Real-time notifications**: < 1s (queueing)
- **Slack auth**: < 2s

### Caching Strategy

```typescript
// Redis caching for frequent lookups
const cache = new Redis(process.env.REDIS_URL);

app.use('/api/sites/:id', async (req, res, next) => {
  const cached = await cache.get(`site:${req.params.id}`);
  if (cached) {
    res.json(JSON.parse(cached));
    return;
  }
  next();
});
```

### Monitoring

- Request/response time tracking
- Error rate monitoring
- Queue depth alerts
- Database connection pool monitoring
